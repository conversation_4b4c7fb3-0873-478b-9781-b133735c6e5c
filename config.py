#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件
用于设置抓取器的各种参数
"""

# 批量处理配置
BATCH_SIZE = 10  # 每次处理的资讯数量，建议5-15之间

# 搜索配置
SEARCH_DAYS_BACK = 3  # 搜索最近几天的内容
SEARCH_RESULTS_PER_KEYWORD = 10  # 每个关键词返回的搜索结果数量

# API调用间隔配置（秒）
SEARCH_DELAY = 1  # 搜索API调用间隔
ANALYSIS_DELAY = 3  # 分析API调用间隔

# 搜索关键词配置
SEARCH_KEYWORDS = [
    "AI coding tools 2024",
    "new AI programming assistant",
    "AI code generator beta",
    "AI developer tools launch",
    "AI IDE extension new",
    "AI code completion tool",
    "programming AI assistant",
    "code AI helper new",
    "AI coding platform beta",
    "developer AI tools 2024",
    "AI pair programming",
    "intelligent code assistant"
]

# 文件输出配置
OUTPUT_ENCODING = 'utf-8'
LOG_LEVEL = 'INFO'  # DEBUG, INFO, WARNING, ERROR

# Gemini模型配置
GEMINI_MODEL = "gemini-2.5-flash"  # 使用的Gemini模型

# 提示词模板配置
BATCH_ANALYSIS_PROMPT_TEMPLATE = """请分析以下多条资讯，判断哪些是关于AI编程工具的信息。请用中文回复。

以下是需要分析的资讯列表：

{news_list}

请对每条资讯按照以下格式分析（请严格按照编号顺序回复）：

资讯X分析:
1. 是否为AI编程工具: [是/否]
2. 工具类型: [如果是AI编程工具，请说明类型，如代码生成、代码补全、IDE插件等；如果不是请写"不适用"]
3. 主要功能: [简要描述主要功能，如果内容是英文请翻译为中文；如果不是AI编程工具请写"不适用"]
4. 是否值得关注: [是/否，说明理由]
5. 简要总结: [用1-2句话总结，如果不是AI编程工具请说明原因]

请确保回复内容为中文，如果原文是英文请翻译为中文。
请严格按照"资讯1分析:"、"资讯2分析:"的格式分隔每条分析结果。
"""
