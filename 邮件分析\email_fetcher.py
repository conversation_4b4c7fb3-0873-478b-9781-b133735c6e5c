"""
邮件获取模块
使用IMAP协议连接邮箱，获取邮件信息
"""

import imaplib
import email
from email.header import decode_header
from email.utils import parsedate_to_datetime
import logging
from typing import List, Dict, Optional
import re
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class EmailFetcher:
    """邮件获取器类"""
    
    def __init__(self, server: str, port: int, username: str, password: str):
        """
        初始化邮件获取器
        
        Args:
            server: IMAP服务器地址
            port: IMAP端口
            username: 用户名
            password: 密码
        """
        self.server = server
        self.port = port
        self.username = username
        self.password = password
        self.mail = None
        
    def connect(self) -> bool:
        """
        连接到邮箱服务器
        
        Returns:
            bool: 连接是否成功
        """
        try:
            # 创建IMAP连接
            if self.port == 993:
                self.mail = imaplib.IMAP4_SSL(self.server, self.port)
            else:
                self.mail = imaplib.IMAP4(self.server, self.port)
                
            # 登录
            self.mail.login(self.username, self.password)
            logger.info(f"成功连接到邮箱: {self.username}")
            return True
            
        except Exception as e:
            logger.error(f"连接邮箱失败: {e}")
            return False
    
    def disconnect(self):
        """断开邮箱连接"""
        if self.mail:
            try:
                self.mail.close()
                self.mail.logout()
                logger.info("已断开邮箱连接")
            except Exception as e:
                logger.error(f"断开连接时出错: {e}")
    
    def decode_mime_words(self, text: str) -> str:
        """
        解码MIME编码的文本
        
        Args:
            text: 待解码的文本
            
        Returns:
            str: 解码后的文本
        """
        if not text:
            return ""
            
        try:
            decoded_parts = decode_header(text)
            decoded_text = ""
            
            for part, encoding in decoded_parts:
                if isinstance(part, bytes):
                    if encoding:
                        decoded_text += part.decode(encoding)
                    else:
                        # 尝试常见编码
                        for enc in ['utf-8', 'gbk', 'gb2312', 'latin-1']:
                            try:
                                decoded_text += part.decode(enc)
                                break
                            except UnicodeDecodeError:
                                continue
                        else:
                            decoded_text += part.decode('utf-8', errors='ignore')
                else:
                    decoded_text += str(part)
                    
            return decoded_text.strip()
            
        except Exception as e:
            logger.warning(f"解码文本失败: {e}, 原文本: {text}")
            return str(text)
    
    def get_email_content(self, msg: email.message.Message) -> str:
        """
        提取邮件正文内容
        
        Args:
            msg: 邮件消息对象
            
        Returns:
            str: 邮件正文
        """
        content = ""
        
        try:
            if msg.is_multipart():
                # 多部分邮件
                for part in msg.walk():
                    content_type = part.get_content_type()
                    content_disposition = str(part.get("Content-Disposition", ""))
                    
                    # 跳过附件
                    if "attachment" in content_disposition:
                        continue
                        
                    if content_type == "text/plain":
                        payload = part.get_payload(decode=True)
                        if payload:
                            charset = part.get_content_charset() or 'utf-8'
                            try:
                                content += payload.decode(charset)
                            except UnicodeDecodeError:
                                # 尝试其他编码
                                for enc in ['gbk', 'gb2312', 'latin-1']:
                                    try:
                                        content += payload.decode(enc)
                                        break
                                    except UnicodeDecodeError:
                                        continue
                                else:
                                    content += payload.decode('utf-8', errors='ignore')
                        break
                        
                    elif content_type == "text/html" and not content:
                        # 如果没有纯文本，使用HTML
                        payload = part.get_payload(decode=True)
                        if payload:
                            charset = part.get_content_charset() or 'utf-8'
                            try:
                                html_content = payload.decode(charset)
                                # 简单去除HTML标签
                                content = re.sub(r'<[^>]+>', '', html_content)
                            except UnicodeDecodeError:
                                content = payload.decode('utf-8', errors='ignore')
                                content = re.sub(r'<[^>]+>', '', content)
            else:
                # 单部分邮件
                payload = msg.get_payload(decode=True)
                if payload:
                    charset = msg.get_content_charset() or 'utf-8'
                    try:
                        content = payload.decode(charset)
                    except UnicodeDecodeError:
                        content = payload.decode('utf-8', errors='ignore')
                        
        except Exception as e:
            logger.error(f"提取邮件内容失败: {e}")
            
        return content.strip()
    
    def fetch_emails(self, folder: str = "INBOX", limit: int = 10) -> List[Dict]:
        """
        获取邮件列表
        
        Args:
            folder: 邮件文件夹名称
            limit: 获取邮件数量限制
            
        Returns:
            List[Dict]: 邮件信息列表
        """
        if not self.mail:
            logger.error("未连接到邮箱")
            return []
            
        emails = []
        
        try:
            # 选择邮件文件夹
            self.mail.select(folder)
            
            # 搜索所有邮件
            status, messages = self.mail.search(None, 'ALL')
            
            if status != 'OK':
                logger.error("搜索邮件失败")
                return []
                
            # 获取邮件ID列表
            email_ids = messages[0].split()
            
            # 限制获取数量，从最新的开始
            email_ids = email_ids[-limit:] if len(email_ids) > limit else email_ids
            email_ids.reverse()  # 最新的在前
            
            logger.info(f"找到 {len(email_ids)} 封邮件")
            
            for email_id in email_ids:
                try:
                    # 获取邮件
                    status, msg_data = self.mail.fetch(email_id, '(RFC822)')
                    
                    if status != 'OK':
                        continue
                        
                    # 解析邮件
                    raw_email = msg_data[0][1]
                    msg = email.message_from_bytes(raw_email)
                    
                    # 提取邮件信息
                    email_info = {
                        'id': email_id.decode(),
                        'subject': self.decode_mime_words(msg.get('Subject', '')),
                        'from': self.decode_mime_words(msg.get('From', '')),
                        'to': self.decode_mime_words(msg.get('To', '')),
                        'date': msg.get('Date', ''),
                        'content': self.get_email_content(msg)
                    }
                    
                    # 解析日期
                    try:
                        if email_info['date']:
                            email_info['datetime'] = parsedate_to_datetime(email_info['date'])
                        else:
                            email_info['datetime'] = datetime.now()
                    except Exception as e:
                        logger.warning(f"解析日期失败: {e}")
                        email_info['datetime'] = datetime.now()
                    
                    emails.append(email_info)
                    logger.info(f"成功获取邮件: {email_info['subject'][:50]}...")
                    
                except Exception as e:
                    logger.error(f"处理邮件 {email_id} 时出错: {e}")
                    continue
                    
        except Exception as e:
            logger.error(f"获取邮件失败: {e}")
            
        return emails


# 常用邮箱服务器配置
EMAIL_SERVERS = {
    'qq': {
        'server': 'imap.qq.com',
        'port': 993
    },
    '163': {
        'server': 'imap.163.com',
        'port': 993
    },
    'gmail': {
        'server': 'imap.gmail.com',
        'port': 993
    },
    'outlook': {
        'server': 'outlook.office365.com',
        'port': 993
    },
    'sina': {
        'server': 'imap.sina.com',
        'port': 993
    }
}


def get_email_server_config(provider: str) -> Optional[Dict]:
    """
    获取邮箱服务器配置
    
    Args:
        provider: 邮箱服务提供商
        
    Returns:
        Dict: 服务器配置信息
    """
    return EMAIL_SERVERS.get(provider.lower())
