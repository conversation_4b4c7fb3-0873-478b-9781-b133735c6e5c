"""
邮件分析系统安装和配置脚本
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path


def install_dependencies():
    """安装依赖包"""
    print("📦 安装依赖包...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "google-genai"])
        print("✅ 依赖包安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖包安装失败: {e}")
        return False


def create_config_file():
    """创建配置文件"""
    config_file = "email_config.ini"
    example_file = "email_config.ini.example"
    
    if os.path.exists(config_file):
        print(f"⚠️ 配置文件 {config_file} 已存在")
        return True
    
    if os.path.exists(example_file):
        try:
            shutil.copy(example_file, config_file)
            print(f"✅ 已创建配置文件: {config_file}")
            print(f"📝 请编辑 {config_file} 文件，填入您的邮箱配置")
            return True
        except Exception as e:
            print(f"❌ 创建配置文件失败: {e}")
            return False
    else:
        print(f"❌ 找不到配置文件模板: {example_file}")
        return False


def check_environment():
    """检查环境配置"""
    print("\n🔍 检查环境配置...")
    
    # 检查Python版本
    python_version = sys.version_info
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 7):
        print(f"❌ Python版本过低: {python_version.major}.{python_version.minor}")
        print("   需要Python 3.7或更高版本")
        return False
    else:
        print(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # 检查API密钥
    api_key = os.getenv('GEMINI_API_KEY')
    if api_key:
        print("✅ GEMINI_API_KEY 环境变量已设置")
    else:
        print("⚠️ GEMINI_API_KEY 环境变量未设置")
        print("   请设置环境变量: set GEMINI_API_KEY=your_api_key (Windows)")
        print("   或: export GEMINI_API_KEY=your_api_key (Linux/Mac)")
    
    return True


def show_usage():
    """显示使用说明"""
    print("\n" + "="*60)
    print("📧 邮件分析系统安装完成")
    print("="*60)
    print("\n📋 下一步操作:")
    print("1. 编辑配置文件 email_config.ini，填入您的邮箱信息")
    print("2. 设置 GEMINI_API_KEY 环境变量")
    print("3. 运行系统: python main.py")
    print("\n💡 常用命令:")
    print("   python main.py --check-config  # 检查配置")
    print("   python main.py --limit 20      # 分析20封邮件")
    print("   python main.py --help          # 查看帮助")
    print("\n📖 详细说明请查看 README.md 文件")
    print("="*60)


def main():
    """主函数"""
    print("🚀 邮件分析系统安装程序")
    print("="*40)
    
    # 检查环境
    if not check_environment():
        print("\n❌ 环境检查失败，请解决上述问题后重试")
        return
    
    # 安装依赖
    if not install_dependencies():
        print("\n❌ 安装失败，请检查网络连接和权限")
        return
    
    # 创建配置文件
    if not create_config_file():
        print("\n❌ 配置文件创建失败")
        return
    
    # 显示使用说明
    show_usage()


if __name__ == "__main__":
    main()
