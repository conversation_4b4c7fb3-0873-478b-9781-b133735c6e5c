"""
配置管理模块
管理邮箱配置和系统设置，支持环境变量和配置文件
"""

import os
import json
import logging
from typing import Dict, Optional
from pathlib import Path
import configparser

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class ConfigManager:
    """配置管理器类"""
    
    def __init__(self, config_file: str = "email_config.ini"):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self.config = configparser.ConfigParser()
        self._load_config()
    
    def _load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                self.config.read(self.config_file, encoding='utf-8')
                logger.info(f"成功加载配置文件: {self.config_file}")
            else:
                logger.info(f"配置文件不存在，将创建默认配置: {self.config_file}")
                self._create_default_config()
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            self._create_default_config()
    
    def _create_default_config(self):
        """创建默认配置"""
        # 邮箱配置
        self.config['EMAIL'] = {
            'provider': 'qq',
            'server': 'imap.qq.com',
            'port': '993',
            'username': '',
            'password': '',
            'folder': 'INBOX'
        }
        
        # 分析配置
        self.config['ANALYSIS'] = {
            'model_name': 'gemini-2.5-pro',
            'batch_size': '10',
            'delay_between_requests': '1.0',
            'min_importance_score': '7'
        }
        
        # 日志配置
        self.config['LOGGING'] = {
            'level': 'INFO',
            'log_file': 'email_analysis.log',
            'max_log_size': '10485760',  # 10MB
            'backup_count': '5'
        }
        
        # 输出配置
        self.config['OUTPUT'] = {
            'save_results': 'true',
            'results_file': 'analysis_results.json',
            'generate_report': 'true',
            'report_file': 'analysis_report.txt'
        }
        
        self.save_config()
    
    def save_config(self):
        """保存配置到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                self.config.write(f)
            logger.info(f"配置已保存到: {self.config_file}")
        except Exception as e:
            logger.error(f"保存配置失败: {e}")
    
    def get_email_config(self) -> Dict:
        """
        获取邮箱配置
        
        Returns:
            Dict: 邮箱配置信息
        """
        email_config = {}
        
        # 优先从环境变量获取
        email_config['provider'] = os.getenv('EMAIL_PROVIDER', 
                                           self.config.get('EMAIL', 'provider', fallback='qq'))
        email_config['server'] = os.getenv('EMAIL_SERVER', 
                                         self.config.get('EMAIL', 'server', fallback='imap.qq.com'))
        email_config['port'] = int(os.getenv('EMAIL_PORT', 
                                           self.config.get('EMAIL', 'port', fallback='993')))
        email_config['username'] = os.getenv('EMAIL_USERNAME', 
                                           self.config.get('EMAIL', 'username', fallback=''))
        email_config['password'] = os.getenv('EMAIL_PASSWORD', 
                                           self.config.get('EMAIL', 'password', fallback=''))
        email_config['folder'] = os.getenv('EMAIL_FOLDER', 
                                         self.config.get('EMAIL', 'folder', fallback='INBOX'))
        
        return email_config
    
    def get_analysis_config(self) -> Dict:
        """
        获取分析配置
        
        Returns:
            Dict: 分析配置信息
        """
        analysis_config = {}
        
        analysis_config['model_name'] = os.getenv('GEMINI_MODEL', 
                                                self.config.get('ANALYSIS', 'model_name', fallback='gemini-2.5-pro'))
        analysis_config['batch_size'] = int(os.getenv('ANALYSIS_BATCH_SIZE', 
                                                    self.config.get('ANALYSIS', 'batch_size', fallback='10')))
        analysis_config['delay'] = float(os.getenv('ANALYSIS_DELAY', 
                                                 self.config.get('ANALYSIS', 'delay_between_requests', fallback='1.0')))
        analysis_config['min_score'] = int(os.getenv('MIN_IMPORTANCE_SCORE', 
                                                   self.config.get('ANALYSIS', 'min_importance_score', fallback='7')))
        
        return analysis_config
    
    def get_logging_config(self) -> Dict:
        """
        获取日志配置
        
        Returns:
            Dict: 日志配置信息
        """
        logging_config = {}
        
        logging_config['level'] = os.getenv('LOG_LEVEL', 
                                          self.config.get('LOGGING', 'level', fallback='INFO'))
        logging_config['log_file'] = os.getenv('LOG_FILE', 
                                             self.config.get('LOGGING', 'log_file', fallback='email_analysis.log'))
        logging_config['max_size'] = int(os.getenv('LOG_MAX_SIZE', 
                                                 self.config.get('LOGGING', 'max_log_size', fallback='10485760')))
        logging_config['backup_count'] = int(os.getenv('LOG_BACKUP_COUNT', 
                                                     self.config.get('LOGGING', 'backup_count', fallback='5')))
        
        return logging_config
    
    def get_output_config(self) -> Dict:
        """
        获取输出配置
        
        Returns:
            Dict: 输出配置信息
        """
        output_config = {}
        
        output_config['save_results'] = os.getenv('SAVE_RESULTS', 
                                                self.config.get('OUTPUT', 'save_results', fallback='true')).lower() == 'true'
        output_config['results_file'] = os.getenv('RESULTS_FILE', 
                                                self.config.get('OUTPUT', 'results_file', fallback='analysis_results.json'))
        output_config['generate_report'] = os.getenv('GENERATE_REPORT', 
                                                   self.config.get('OUTPUT', 'generate_report', fallback='true')).lower() == 'true'
        output_config['report_file'] = os.getenv('REPORT_FILE', 
                                               self.config.get('OUTPUT', 'report_file', fallback='analysis_report.txt'))
        
        return output_config
    
    def validate_email_config(self, config: Dict) -> bool:
        """
        验证邮箱配置
        
        Args:
            config: 邮箱配置
            
        Returns:
            bool: 配置是否有效
        """
        required_fields = ['server', 'port', 'username', 'password']
        
        for field in required_fields:
            if not config.get(field):
                logger.error(f"邮箱配置缺少必需字段: {field}")
                return False
        
        # 验证端口号
        try:
            port = int(config['port'])
            if port <= 0 or port > 65535:
                logger.error(f"无效的端口号: {port}")
                return False
        except ValueError:
            logger.error(f"端口号必须是数字: {config['port']}")
            return False
        
        return True
    
    def check_gemini_api_key(self) -> bool:
        """
        检查Gemini API密钥是否设置
        
        Returns:
            bool: API密钥是否已设置
        """
        api_key = os.getenv('GEMINI_API_KEY')
        if not api_key:
            logger.error("未设置GEMINI_API_KEY环境变量")
            return False
        return True
    
    def update_email_config(self, **kwargs):
        """
        更新邮箱配置
        
        Args:
            **kwargs: 配置参数
        """
        if 'EMAIL' not in self.config:
            self.config['EMAIL'] = {}
        
        for key, value in kwargs.items():
            if value is not None:
                self.config['EMAIL'][key] = str(value)
        
        self.save_config()
        logger.info("邮箱配置已更新")
    
    def update_analysis_config(self, **kwargs):
        """
        更新分析配置
        
        Args:
            **kwargs: 配置参数
        """
        if 'ANALYSIS' not in self.config:
            self.config['ANALYSIS'] = {}
        
        for key, value in kwargs.items():
            if value is not None:
                self.config['ANALYSIS'][key] = str(value)
        
        self.save_config()
        logger.info("分析配置已更新")
    
    def get_all_config(self) -> Dict:
        """
        获取所有配置
        
        Returns:
            Dict: 完整配置信息
        """
        return {
            'email': self.get_email_config(),
            'analysis': self.get_analysis_config(),
            'logging': self.get_logging_config(),
            'output': self.get_output_config()
        }
    
    def print_config_status(self):
        """打印配置状态"""
        print("\n=== 邮件分析系统配置状态 ===")
        
        # 邮箱配置
        email_config = self.get_email_config()
        print(f"\n📧 邮箱配置:")
        print(f"  服务商: {email_config['provider']}")
        print(f"  服务器: {email_config['server']}:{email_config['port']}")
        print(f"  用户名: {email_config['username'] or '未设置'}")
        print(f"  密码: {'已设置' if email_config['password'] else '未设置'}")
        print(f"  文件夹: {email_config['folder']}")
        
        # API配置
        print(f"\n🤖 API配置:")
        print(f"  Gemini API Key: {'已设置' if self.check_gemini_api_key() else '未设置'}")
        
        # 分析配置
        analysis_config = self.get_analysis_config()
        print(f"\n🔍 分析配置:")
        print(f"  模型: {analysis_config['model_name']}")
        print(f"  批处理大小: {analysis_config['batch_size']}")
        print(f"  请求延迟: {analysis_config['delay']}秒")
        print(f"  最低重要性评分: {analysis_config['min_score']}")
        
        # 输出配置
        output_config = self.get_output_config()
        print(f"\n📄 输出配置:")
        print(f"  保存结果: {output_config['save_results']}")
        print(f"  结果文件: {output_config['results_file']}")
        print(f"  生成报告: {output_config['generate_report']}")
        print(f"  报告文件: {output_config['report_file']}")
        
        print("\n" + "="*40)
